package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;

/**
 * Manager pro správu obchodu
 */
public class ShopManager {
    
    private final HypixelSkyBlockCZ plugin;
    private final Map<Material, Double> buyPrices;
    private final Map<Material, Double> sellPrices;
    
    public ShopManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.buyPrices = new HashMap<>();
        this.sellPrices = new HashMap<>();
        
        loadPrices();
    }
    
    /**
     * Načte ceny z konfigurace
     */
    private void loadPrices() {
        // TODO: Načíst ceny z config.yml
        // Zatím hardcoded ceny
        buyPrices.put(Material.DIRT, 1.0);
        buyPrices.put(Material.STONE, 2.0);
        buyPrices.put(Material.OAK_LOG, 5.0);
        buyPrices.put(Material.IRON_INGOT, 10.0);
        buyPrices.put(Material.DIAMOND, 50.0);
        
        sellPrices.put(Material.DIRT, 0.5);
        sellPrices.put(Material.STONE, 1.0);
        sellPrices.put(Material.OAK_LOG, 2.5);
        sellPrices.put(Material.IRON_INGOT, 5.0);
        sellPrices.put(Material.DIAMOND, 25.0);
    }
    
    /**
     * Získá nákupní cenu itemu
     */
    public double getBuyPrice(Material material) {
        return buyPrices.getOrDefault(material, 0.0);
    }
    
    /**
     * Získá prodejní cenu itemu
     */
    public double getSellPrice(Material material) {
        return sellPrices.getOrDefault(material, 0.0);
    }
    
    /**
     * Koupí item pro hráče
     */
    public boolean buyItem(Player player, Material material, int amount) {
        double totalPrice = getBuyPrice(material) * amount;
        
        // TODO: Zkontrolovat, jestli má hráč dostatek mincí
        // TODO: Odečíst mince
        // TODO: Přidat item do inventáře
        
        return true;
    }
    
    /**
     * Prodá item od hráče
     */
    public boolean sellItem(Player player, Material material, int amount) {
        double totalPrice = getSellPrice(material) * amount;
        
        // TODO: Zkontrolovat, jestli má hráč dostatek itemů
        // TODO: Odebrat itemy z inventáře
        // TODO: Přidat mince
        
        return true;
    }
}
