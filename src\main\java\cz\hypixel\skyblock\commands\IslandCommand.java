package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Příkaz /island pro správu ostrova
 */
public class IslandCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;

    public IslandCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Component.text("Tento příkaz může používat pouze hráč!", NamedTextColor.RED));
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            sendHelpMessage(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "create":
            case "vytvorit":
                createIsland(player);
                break;
            case "home":
            case "domov":
                goToIsland(player);
                break;
            case "delete":
            case "smazat":
                deleteIsland(player);
                break;
            case "invite":
            case "pozvat":
                if (args.length < 2) {
                    player.sendMessage(Component.text("Použití: /island invite <hráč>", NamedTextColor.RED));
                    return true;
                }
                invitePlayer(player, args[1]);
                break;
            case "kick":
            case "vyhodit":
                if (args.length < 2) {
                    player.sendMessage(Component.text("Použití: /island kick <hráč>", NamedTextColor.RED));
                    return true;
                }
                kickPlayer(player, args[1]);
                break;
            case "help":
            case "pomoc":
                sendHelpMessage(player);
                break;
            default:
                player.sendMessage(Component.text("Neznámý příkaz! Použij /island help", NamedTextColor.RED));
                break;
        }

        return true;
    }

    private void sendHelpMessage(Player player) {
        player.sendMessage(Component.text("=== Správa Ostrova ===", NamedTextColor.GOLD));
        player.sendMessage(Component.text("/island create - Vytvoří nový ostrov", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/island home - Teleportuje na ostrov", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/island delete - Smaže ostrov", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/island invite <hráč> - Pozve hráče", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/island kick <hráč> - Vyhodí hráče", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("=====================", NamedTextColor.GOLD));
    }

    private void createIsland(Player player) {
        // TODO: Implementovat vytvoření ostrova
        player.sendMessage(Component.text("Vytváří se tvůj ostrov...", NamedTextColor.GREEN));
        player.sendMessage(Component.text("Ostrov byl úspěšně vytvořen!", NamedTextColor.GREEN));
    }

    private void goToIsland(Player player) {
        // TODO: Implementovat teleportaci na ostrov
        player.sendMessage(Component.text("Teleportuješ se na svůj ostrov...", NamedTextColor.GREEN));
    }

    private void deleteIsland(Player player) {
        // TODO: Implementovat smazání ostrova
        player.sendMessage(Component.text("Opravdu chceš smazat svůj ostrov? Napiš /island confirm", NamedTextColor.RED));
    }

    private void invitePlayer(Player player, String targetName) {
        // TODO: Implementovat pozvání hráče
        player.sendMessage(Component.text("Pozval jsi hráče " + targetName + " na svůj ostrov!", NamedTextColor.GREEN));
    }

    private void kickPlayer(Player player, String targetName) {
        // TODO: Implementovat vyhození hráče
        player.sendMessage(Component.text("Vyhodil jsi hráče " + targetName + " ze svého ostrova!", NamedTextColor.GREEN));
    }
}
