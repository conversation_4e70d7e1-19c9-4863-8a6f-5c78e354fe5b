# OpenCode konfigurace pro Ollama
# Tento soubor ukazuje jak nakonfigurovat OpenCode pro použití s Ollama

# <PERSON><PERSON><PERSON> schvalování všech operací (doporučeno pro lokální vývoj)
autoApprove: true

# Debug mód pro detailní logy
debug: false

# <PERSON><PERSON><PERSON><PERSON><PERSON> (volitelné)
# wd: "/path/to/your/project"

# Provider konfigurace
providers:
  # Ollama konfigurace
  ollama:
    apiKey: "ollama"  # <PERSON><PERSON> ho<PERSON>, není potřeba skutečný API klíč
    disabled: false

# Agent konfigurace - různé modely pro různé úkoly
agents:
  # <PERSON>lavní kódovací agent - použ<PERSON>v<PERSON> nejlepší model pro programování
  coder:
    model: "ollama.deepseek-coder"  # Nebo "ollama.codellama", "ollama.llama3.1:8b"
    maxTokens: 4096
    
  # Sumarizační agent - mů<PERSON><PERSON> použ<PERSON><PERSON> men<PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON> model
  summarizer:
    model: "ollama.llama3.2:3b"
    maxTokens: 2048
    
  # Task agent - pro pl<PERSON><PERSON>n<PERSON> a organizaci úkolů
  task:
    model: "ollama.llama3.1:8b"
    maxTokens: 2048
    
  # Title agent - pro generování názvů, může být velmi rychlý
  title:
    model: "ollama.llama3.2:1b"
    maxTokens: 512

# Shell konfigurace
shell:
  path: "cmd.exe"      # Windows
  # path: "/bin/bash"  # Linux/macOS
  args: ["/Q"]         # Windows
  # args: ["-l"]       # Linux/macOS

# TUI konfigurace
tui:
  theme: "opencode"

# Automatické kompaktování dat
autoCompact: true

# Context paths - soubory a adresáře které má AI vždy k dispozici
contextPaths:
  - "README.md"
  - "go.mod"
  - "package.json"
  - "requirements.txt"
  - "Cargo.toml"
  - ".env.example"

# Data adresář pro ukládání konverzací a cache
data:
  directory: "~/.opencode"

---
# Alternativní konfigurace pro různé scénáře

# Konfigurace pro rychlý vývoj (malé modely)
fast_development:
  agents:
    coder:
      model: "ollama.llama3.2:3b"
      maxTokens: 2048
    summarizer:
      model: "ollama.llama3.2:1b"
      maxTokens: 1024

# Konfigurace pro kvalitní výsledky (velké modely)
high_quality:
  agents:
    coder:
      model: "ollama.llama3.1:70b"  # Vyžaduje silný hardware
      maxTokens: 8192
    summarizer:
      model: "ollama.llama3.1:8b"
      maxTokens: 4096

# Konfigurace pro specializované kódování
coding_focused:
  agents:
    coder:
      model: "ollama.codellama"
      maxTokens: 4096
    summarizer:
      model: "ollama.deepseek-coder"
      maxTokens: 2048
