package cz.hypixel.skyblock.listeners;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Event listener pro hráče
 */
public class PlayerListener implements Listener {
    
    private final HypixelSkyBlockCZ plugin;
    
    public PlayerListener(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Event při připojení hráče
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        // TODO: Načíst data hráče
        // TODO: Zkontrolovat, jestli má ostrov
        
        event.getPlayer().sendMessage(
            Component.text("Vítej v HypixelSkyBlockCZ!", NamedTextColor.GREEN)
        );
        
        plugin.getLogger().info("Hráč " + event.getPlayer().getName() + " se připojil");
    }
    
    /**
     * Event při odpojení hráče
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // TODO: Uložit data hráče
        
        plugin.getLogger().info("Hráč " + event.getPlayer().getName() + " se odpojil");
    }
}
