# Ollama Integration with OpenCode

OpenCode má vestavěnou podporu pro Ollama - lokální LLM runtime, který umožňuje spouštět velké jazykové modely přímo na vašem počítači.

## Instalace Ollama

### Windows
```bash
# Stáhněte a nainstalujte z oficiálních stránek
# https://ollama.ai/download/windows
```

### macOS
```bash
# Homebrew
brew install ollama

# Nebo stáhněte z oficiálních stránek
# https://ollama.ai/download/macos
```

### Linux
```bash
# Instalace přes curl
curl -fsSL https://ollama.ai/install.sh | sh

# Nebo přes package manager
# Ubuntu/Debian
sudo apt install ollama

# Arch Linux
yay -S ollama
```

## Spuštění Ollama

```bash
# Spusťte Ollama server
ollama serve

# Server běží na http://localhost:11434
```

## Stažení modelů

```bash
# Doporučené modely pro kódování
ollama pull llama3.2:3b          # <PERSON><PERSON><PERSON><PERSON>, malý model
ollama pull llama3.1:8b          # Vyvážený model
ollama pull codellama            # Specializovaný na kód
ollama pull deepseek-coder       # Výborný pro programování

# Další užitečné modely
ollama pull mistral              # Obecný model
ollama pull gemma2               # Google model
ollama pull qwen2.5              # Alibaba model
```

## Konfigurace OpenCode

OpenCode automaticky detekuje běžící Ollama server a načte dostupné modely.

### Automatická konfigurace
Pokud Ollama běží na standardním portu (11434), OpenCode ho automaticky najde a použije.

### Vlastní endpoint
```yaml
# config.yaml
providers:
  ollama:
    apiKey: "ollama"  # Dummy hodnota, není potřeba skutečný klíč

# Nebo nastavte environment variable
export OLLAMA_HOST=http://localhost:11434
```

### Výběr modelu
```yaml
# config.yaml
agents:
  coder:
    model: "ollama.llama3.1:8b"
    maxTokens: 4096
  summarizer:
    model: "ollama.llama3.2:3b"
    maxTokens: 2048
```

## Použití

```bash
# OpenCode automaticky použije Ollama modely pokud jsou dostupné
opencode

# S auto-approve pro plně automatizovaný workflow
opencode -a

# S konkrétním promptem
opencode -p "Vytvoř novou Go funkci pro HTTP server"

# S debug módem
opencode -d
```

## Výhody Ollama

✅ **Lokální běh** - Žádné API klíče, žádné externí závislosti  
✅ **Rychlost** - Nízká latence, žádné síťové požadavky  
✅ **Soukromí** - Váš kód nikdy neopustí váš počítač  
✅ **Zdarma** - Žádné náklady na API volání  
✅ **Offline** - Funguje bez internetového připojení  

## Doporučené modely podle použití

### Pro rychlé úkoly (1-3B parametrů)
- `llama3.2:1b` - Nejrychlejší, základní úkoly
- `llama3.2:3b` - Dobrý kompromis rychlost/kvalita

### Pro kódování (7-8B parametrů)
- `codellama` - Specializovaný na programování
- `deepseek-coder` - Výborný pro složité kódovací úkoly
- `llama3.1:8b` - Univerzální, dobrý pro kód i text

### Pro složité úkoly (70B+ parametrů)
- `llama3.1:70b` - Nejkvalitnější, vyžaduje silný hardware

## Troubleshooting

### Ollama není detekován
```bash
# Zkontrolujte, že Ollama běží
curl http://localhost:11434

# Zkontrolujte dostupné modely
ollama list

# Restartujte Ollama
ollama serve
```

### Pomalý výkon
```bash
# Použijte menší model
ollama pull llama3.2:3b

# Nebo nastavte GPU akceleraci (pokud máte NVIDIA GPU)
# Ollama automaticky použije GPU pokud je dostupné
```

### Chyby paměti
```bash
# Použijte kvantizovaný model
ollama pull llama3.1:8b-q4_0

# Nebo zvyšte swap
sudo swapon --show
```

## Environment Variables

```bash
# Ollama endpoint (výchozí: http://localhost:11434)
export OLLAMA_HOST=http://localhost:11434

# Ollama modely adresář
export OLLAMA_MODELS=/path/to/models

# GPU layers (pro NVIDIA GPU)
export OLLAMA_NUM_GPU_LAYERS=35
```

## Příklady použití

```bash
# Základní použití s Ollama
opencode -p "Vysvětli tento kód" -a

# Vytvoření nového souboru
opencode -a -p "Vytvoř REST API server v Go s gin frameworkem"

# Refaktoring kódu
opencode -a -p "Refaktoruj tento kód aby byl více čitelný"

# Code review
opencode -a -p "Zkontroluj tento kód a navrhni zlepšení"
```
