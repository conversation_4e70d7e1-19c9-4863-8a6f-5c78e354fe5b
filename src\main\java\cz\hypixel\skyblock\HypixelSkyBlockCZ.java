package cz.hypixel.skyblock;

import cz.hypixel.skyblock.commands.SkyBlockCommand;
import cz.hypixel.skyblock.commands.IslandCommand;
import cz.hypixel.skyblock.commands.ShopCommand;
import cz.hypixel.skyblock.listeners.PlayerListener;
import cz.hypixel.skyblock.managers.IslandManager;
import cz.hypixel.skyblock.managers.PlayerDataManager;
import cz.hypixel.skyblock.managers.ShopManager;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Logger;

/**
 * Hlavní třída pluginu HypixelSkyBlockCZ
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class HypixelSkyBlockCZ extends JavaPlugin {

    private static HypixelSkyBlockCZ instance;
    private Logger logger;
    
    // Managers
    private IslandManager islandManager;
    private PlayerDataManager playerDataManager;
    private ShopManager shopManager;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();
        
        logger.info("=== HypixelSkyBlockCZ ===");
        logger.info("Plugin se spouští...");
        
        // Inicializace konfigurace
        saveDefaultConfig();
        
        // Inicializace managerů
        initializeManagers();
        
        // Registrace příkazů
        registerCommands();
        
        // Registrace event listenerů
        registerListeners();
        
        logger.info("Plugin úspěšně spuštěn!");
        logger.info("Verze: " + getDescription().getVersion());
        logger.info("Autor: " + getDescription().getAuthors());
        logger.info("========================");
    }

    @Override
    public void onDisable() {
        logger.info("=== HypixelSkyBlockCZ ===");
        logger.info("Plugin se vypína...");
        
        // Uložení dat
        if (playerDataManager != null) {
            playerDataManager.saveAllData();
        }
        
        if (islandManager != null) {
            islandManager.saveAllIslands();
        }
        
        logger.info("Plugin úspěšně vypnut!");
        logger.info("========================");
    }
    
    /**
     * Inicializace všech managerů
     */
    private void initializeManagers() {
        logger.info("Inicializace managerů...");
        
        playerDataManager = new PlayerDataManager(this);
        islandManager = new IslandManager(this);
        shopManager = new ShopManager(this);
        
        logger.info("Managery úspěšně inicializovány!");
    }
    
    /**
     * Registrace všech příkazů
     */
    private void registerCommands() {
        logger.info("Registrace příkazů...");
        
        getCommand("skyblock").setExecutor(new SkyBlockCommand(this));
        getCommand("island").setExecutor(new IslandCommand(this));
        getCommand("shop").setExecutor(new ShopCommand(this));
        
        logger.info("Příkazy úspěšně zaregistrovány!");
    }
    
    /**
     * Registrace všech event listenerů
     */
    private void registerListeners() {
        logger.info("Registrace event listenerů...");
        
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        
        logger.info("Event listenery úspěšně zaregistrovány!");
    }
    
    // Gettery pro přístup k managerům
    
    public static HypixelSkyBlockCZ getInstance() {
        return instance;
    }
    
    public IslandManager getIslandManager() {
        return islandManager;
    }
    
    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }
    
    public ShopManager getShopManager() {
        return shopManager;
    }
}
