package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * Příkaz /shop pro SkyBlock obchod
 */
public class ShopCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;

    public ShopCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Component.text("Tento příkaz může používat pouze hráč!", NamedTextColor.RED));
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            openShopGUI(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "buy":
            case "koupit":
                if (args.length < 3) {
                    player.sendMessage(Component.text("Použití: /shop buy <item> <množství>", NamedTextColor.RED));
                    return true;
                }
                buyItem(player, args[1], args[2]);
                break;
            case "sell":
            case "prodat":
                if (args.length < 3) {
                    player.sendMessage(Component.text("Použití: /shop sell <item> <množství>", NamedTextColor.RED));
                    return true;
                }
                sellItem(player, args[1], args[2]);
                break;
            case "list":
            case "seznam":
                showItemList(player);
                break;
            case "help":
            case "pomoc":
                sendHelpMessage(player);
                break;
            default:
                openShopGUI(player);
                break;
        }

        return true;
    }

    private void sendHelpMessage(Player player) {
        player.sendMessage(Component.text("=== SkyBlock Obchod ===", NamedTextColor.GOLD));
        player.sendMessage(Component.text("/shop - Otevře obchod", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/shop buy <item> <množství> - Koupí item", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/shop sell <item> <množství> - Prodá item", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/shop list - Seznam všech itemů", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("======================", NamedTextColor.GOLD));
    }

    private void openShopGUI(Player player) {
        // TODO: Implementovat GUI obchodu
        player.sendMessage(Component.text("Otevírá se obchod...", NamedTextColor.GREEN));
        player.sendMessage(Component.text("GUI obchod bude implementován později!", NamedTextColor.YELLOW));
    }

    private void buyItem(Player player, String itemName, String amountStr) {
        try {
            int amount = Integer.parseInt(amountStr);
            Material material = Material.valueOf(itemName.toUpperCase());
            
            // TODO: Implementovat nákup itemů
            player.sendMessage(Component.text("Koupil jsi " + amount + "x " + material.name() + "!", NamedTextColor.GREEN));
            
            // Přidání itemů do inventáře
            ItemStack item = new ItemStack(material, amount);
            player.getInventory().addItem(item);
            
        } catch (NumberFormatException e) {
            player.sendMessage(Component.text("Neplatné množství!", NamedTextColor.RED));
        } catch (IllegalArgumentException e) {
            player.sendMessage(Component.text("Neplatný item!", NamedTextColor.RED));
        }
    }

    private void sellItem(Player player, String itemName, String amountStr) {
        try {
            int amount = Integer.parseInt(amountStr);
            Material material = Material.valueOf(itemName.toUpperCase());
            
            // TODO: Implementovat prodej itemů
            player.sendMessage(Component.text("Prodal jsi " + amount + "x " + material.name() + "!", NamedTextColor.GREEN));
            
        } catch (NumberFormatException e) {
            player.sendMessage(Component.text("Neplatné množství!", NamedTextColor.RED));
        } catch (IllegalArgumentException e) {
            player.sendMessage(Component.text("Neplatný item!", NamedTextColor.RED));
        }
    }

    private void showItemList(Player player) {
        player.sendMessage(Component.text("=== Seznam Itemů ===", NamedTextColor.GOLD));
        player.sendMessage(Component.text("DIRT - 1 mince", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("STONE - 2 mince", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("WOOD - 5 mincí", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("IRON_INGOT - 10 mincí", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("DIAMOND - 50 mincí", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("===================", NamedTextColor.GOLD));
    }
}
