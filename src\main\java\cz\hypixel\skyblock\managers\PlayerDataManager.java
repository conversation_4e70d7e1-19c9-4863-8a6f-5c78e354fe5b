package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;

/**
 * Manager pro správu dat hráčů
 */
public class PlayerDataManager {
    
    private final HypixelSkyBlockCZ plugin;
    
    public PlayerDataManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Uloží všechna data hráčů
     */
    public void saveAllData() {
        plugin.getLogger().info("Ukládání dat hráčů...");
        // TODO: Implementovat ukládání dat hráčů
    }
    
    /**
     * Načte data hráče
     */
    public void loadPlayerData(String playerName) {
        // TODO: Implementovat načítání dat hráče
    }
    
    /**
     * Uloží data hráče
     */
    public void savePlayerData(String playerName) {
        // TODO: Implementovat ukládání dat hráče
    }
}
