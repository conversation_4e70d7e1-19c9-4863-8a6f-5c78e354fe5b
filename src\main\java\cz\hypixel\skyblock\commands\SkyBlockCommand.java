package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Hlavní příkaz /skyblock
 */
public class SkyBlockCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;

    public SkyBlockCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Component.text("Tento příkaz může používat pouze hráč!", NamedTextColor.RED));
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            sendHelpMessage(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "help":
            case "pomoc":
                sendHelpMessage(player);
                break;
            case "info":
                sendInfoMessage(player);
                break;
            case "stats":
            case "statistiky":
                sendStatsMessage(player);
                break;
            case "reload":
                if (player.hasPermission("skyblock.admin")) {
                    plugin.reloadConfig();
                    player.sendMessage(Component.text("Konfigurace byla znovu načtena!", NamedTextColor.GREEN));
                } else {
                    player.sendMessage(Component.text("Nemáš oprávnění k tomuto příkazu!", NamedTextColor.RED));
                }
                break;
            default:
                player.sendMessage(Component.text("Neznámý příkaz! Použij /skyblock help", NamedTextColor.RED));
                break;
        }

        return true;
    }

    private void sendHelpMessage(Player player) {
        player.sendMessage(Component.text("=== HypixelSkyBlockCZ Nápověda ===", NamedTextColor.GOLD));
        player.sendMessage(Component.text("/skyblock help - Zobrazí tuto nápovědu", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/skyblock info - Informace o pluginu", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/skyblock stats - Tvoje statistiky", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/island - Správa ostrova", NamedTextColor.YELLOW));
        player.sendMessage(Component.text("/shop - SkyBlock obchod", NamedTextColor.YELLOW));
        
        if (player.hasPermission("skyblock.admin")) {
            player.sendMessage(Component.text("/skyblock reload - Znovu načte konfiguraci", NamedTextColor.AQUA));
        }
        
        player.sendMessage(Component.text("================================", NamedTextColor.GOLD));
    }

    private void sendInfoMessage(Player player) {
        player.sendMessage(Component.text("=== HypixelSkyBlockCZ Info ===", NamedTextColor.GOLD));
        player.sendMessage(Component.text("Verze: " + plugin.getDescription().getVersion(), NamedTextColor.GREEN));
        player.sendMessage(Component.text("Autor: " + plugin.getDescription().getAuthors(), NamedTextColor.GREEN));
        player.sendMessage(Component.text("Popis: " + plugin.getDescription().getDescription(), NamedTextColor.GREEN));
        player.sendMessage(Component.text("=============================", NamedTextColor.GOLD));
    }

    private void sendStatsMessage(Player player) {
        // TODO: Implementovat zobrazení statistik hráče
        player.sendMessage(Component.text("=== Tvoje Statistiky ===", NamedTextColor.GOLD));
        player.sendMessage(Component.text("Jméno: " + player.getName(), NamedTextColor.GREEN));
        player.sendMessage(Component.text("Level: 1", NamedTextColor.GREEN));
        player.sendMessage(Component.text("Mince: 0", NamedTextColor.GREEN));
        player.sendMessage(Component.text("Ostrov: Žádný", NamedTextColor.GREEN));
        player.sendMessage(Component.text("=======================", NamedTextColor.GOLD));
    }
}
