package models

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/opencode-ai/opencode/internal/logging"
	"github.com/spf13/viper"
)

// Helper function for minimum of two int64 values
func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

const (
	// Default Ollama endpoint
	DefaultOllamaEndpoint = "http://localhost:11434"
	
	// Ollama API paths
	ollamaModelsPath = "api/tags"
	ollamaGeneratePath = "api/generate"
	ollamaChatPath = "api/chat"
)

// Popular Ollama model IDs
const (
	OllamaLlama3_2_3B    ModelID = "ollama.llama3.2:3b"
	OllamaLlama3_2_1B    ModelID = "ollama.llama3.2:1b"
	OllamaLlama3_1_8B    ModelID = "ollama.llama3.1:8b"
	OllamaLlama3_1_70B   ModelID = "ollama.llama3.1:70b"
	OllamaCodeLlama      ModelID = "ollama.codellama"
	OllamaDeepSeekCoder  ModelID = "ollama.deepseek-coder"
	OllamaMistral        ModelID = "ollama.mistral"
	OllamaGemma2         ModelID = "ollama.gemma2"
	OllamaQwen2_5        ModelID = "ollama.qwen2.5"
)

// Static Ollama models that are commonly available
var OllamaModels = map[ModelID]Model{
	OllamaLlama3_2_3B: {
		ID:                  OllamaLlama3_2_3B,
		Name:                "Llama 3.2 3B",
		Provider:            ProviderOllama,
		APIModel:            "llama3.2:3b",
		CostPer1MIn:         0, // Local models are free
		CostPer1MOut:        0,
		CostPer1MInCached:   0,
		CostPer1MOutCached:  0,
		ContextWindow:       131072,
		DefaultMaxTokens:    4096,
		CanReason:           true,
		SupportsAttachments: false,
	},
	OllamaLlama3_2_1B: {
		ID:                  OllamaLlama3_2_1B,
		Name:                "Llama 3.2 1B",
		Provider:            ProviderOllama,
		APIModel:            "llama3.2:1b",
		CostPer1MIn:         0,
		CostPer1MOut:        0,
		CostPer1MInCached:   0,
		CostPer1MOutCached:  0,
		ContextWindow:       131072,
		DefaultMaxTokens:    4096,
		CanReason:           true,
		SupportsAttachments: false,
	},
	OllamaLlama3_1_8B: {
		ID:                  OllamaLlama3_1_8B,
		Name:                "Llama 3.1 8B",
		Provider:            ProviderOllama,
		APIModel:            "llama3.1:8b",
		CostPer1MIn:         0,
		CostPer1MOut:        0,
		CostPer1MInCached:   0,
		CostPer1MOutCached:  0,
		ContextWindow:       131072,
		DefaultMaxTokens:    4096,
		CanReason:           true,
		SupportsAttachments: false,
	},
	OllamaCodeLlama: {
		ID:                  OllamaCodeLlama,
		Name:                "Code Llama",
		Provider:            ProviderOllama,
		APIModel:            "codellama",
		CostPer1MIn:         0,
		CostPer1MOut:        0,
		CostPer1MInCached:   0,
		CostPer1MOutCached:  0,
		ContextWindow:       16384,
		DefaultMaxTokens:    4096,
		CanReason:           true,
		SupportsAttachments: false,
	},
	OllamaDeepSeekCoder: {
		ID:                  OllamaDeepSeekCoder,
		Name:                "DeepSeek Coder",
		Provider:            ProviderOllama,
		APIModel:            "deepseek-coder",
		CostPer1MIn:         0,
		CostPer1MOut:        0,
		CostPer1MInCached:   0,
		CostPer1MOutCached:  0,
		ContextWindow:       16384,
		DefaultMaxTokens:    4096,
		CanReason:           true,
		SupportsAttachments: false,
	},
}

type ollamaModelList struct {
	Models []ollamaModel `json:"models"`
}

type ollamaModel struct {
	Name       string    `json:"name"`
	Model      string    `json:"model"`
	ModifiedAt time.Time `json:"modified_at"`
	Size       int64     `json:"size"`
	Digest     string    `json:"digest"`
	Details    struct {
		ParentModel       string   `json:"parent_model"`
		Format            string   `json:"format"`
		Family            string   `json:"family"`
		Families          []string `json:"families"`
		ParameterSize     string   `json:"parameter_size"`
		QuantizationLevel string   `json:"quantization_level"`
	} `json:"details"`
}

func init() {
	// Check if Ollama is available
	endpoint := getOllamaEndpoint()
	if isOllamaAvailable(endpoint) {
		logging.Info("Ollama detected, loading available models", "endpoint", endpoint)
		
		// Load dynamic models from Ollama
		models := listOllamaModels(endpoint)
		if len(models) > 0 {
			loadOllamaModels(models)
			
			// Set dummy API key for Ollama (not needed but required by config)
			viper.SetDefault("providers.ollama.apiKey", "ollama")
			
			// Set Ollama as high priority if it has models
			ProviderPopularity[ProviderOllama] = 0
			
			logging.Info("Loaded Ollama models", "count", len(models))
		}
	} else {
		logging.Debug("Ollama not available", "endpoint", endpoint)
	}
}

func getOllamaEndpoint() string {
	if endpoint := os.Getenv("OLLAMA_HOST"); endpoint != "" {
		return endpoint
	}
	return DefaultOllamaEndpoint
}

func isOllamaAvailable(endpoint string) bool {
	client := &http.Client{Timeout: 2 * time.Second}
	resp, err := client.Get(endpoint)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

func listOllamaModels(endpoint string) []ollamaModel {
	modelsURL := fmt.Sprintf("%s/%s", strings.TrimSuffix(endpoint, "/"), ollamaModelsPath)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(modelsURL)
	if err != nil {
		logging.Debug("Failed to list Ollama models", "error", err, "url", modelsURL)
		return []ollamaModel{}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logging.Debug("Failed to list Ollama models", "status", resp.StatusCode, "url", modelsURL)
		return []ollamaModel{}
	}

	var modelList ollamaModelList
	if err := json.NewDecoder(resp.Body).Decode(&modelList); err != nil {
		logging.Debug("Failed to decode Ollama models", "error", err, "url", modelsURL)
		return []ollamaModel{}
	}

	return modelList.Models
}

func loadOllamaModels(models []ollamaModel) {
	for i, m := range models {
		model := convertOllamaModel(m)
		SupportedModels[model.ID] = model

		// Set the first model as default for all agents
		if i == 0 {
			viper.SetDefault("agents.coder.model", model.ID)
			viper.SetDefault("agents.summarizer.model", model.ID)
			viper.SetDefault("agents.task.model", model.ID)
			viper.SetDefault("agents.title.model", model.ID)
		}
	}
}

func convertOllamaModel(model ollamaModel) Model {
	// Use the model name as both ID and API model
	modelName := model.Name
	if modelName == "" {
		modelName = model.Model
	}

	// Create a friendly display name
	displayName := friendlyOllamaModelName(modelName)

	// Estimate context window based on model family
	contextWindow := int64(4096) // Default
	if strings.Contains(strings.ToLower(modelName), "llama3") {
		contextWindow = 131072
	} else if strings.Contains(strings.ToLower(modelName), "codellama") {
		contextWindow = 16384
	}

	return Model{
		ID:                  ModelID("ollama." + modelName),
		Name:                displayName,
		Provider:            ProviderOllama,
		APIModel:            modelName,
		CostPer1MIn:         0, // Local models are free
		CostPer1MOut:        0,
		CostPer1MInCached:   0,
		CostPer1MOutCached:  0,
		ContextWindow:       contextWindow,
		DefaultMaxTokens:    min(contextWindow/4, 4096),
		CanReason:           true,
		SupportsAttachments: false,
	}
}

func friendlyOllamaModelName(modelName string) string {
	// Remove common prefixes and make it more readable
	name := modelName
	
	// Remove version tags like :latest, :7b, etc.
	if colonIndex := strings.Index(name, ":"); colonIndex != -1 {
		tag := name[colonIndex+1:]
		name = name[:colonIndex]
		
		// Add back meaningful tags
		if tag != "latest" {
			name = name + " " + strings.ToUpper(tag)
		}
	}
	
	// Capitalize and format common model names
	switch strings.ToLower(name) {
	case "llama3.2":
		return "Llama 3.2"
	case "llama3.1":
		return "Llama 3.1"
	case "llama3":
		return "Llama 3"
	case "codellama":
		return "Code Llama"
	case "deepseek-coder":
		return "DeepSeek Coder"
	case "mistral":
		return "Mistral"
	case "gemma2":
		return "Gemma 2"
	case "qwen2.5":
		return "Qwen 2.5"
	default:
		// Capitalize first letter
		if len(name) > 0 {
			return strings.ToUpper(name[:1]) + name[1:]
		}
		return name
	}
}
